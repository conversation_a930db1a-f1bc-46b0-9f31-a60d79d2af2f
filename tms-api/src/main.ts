import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from './pipes/validation.pipe';
import { GlobalExceptionFilter } from './filters/global-exception.filter';
import { LoggingInterceptor } from './interceptors/logging.interceptor';

import { DEFAULTS } from './common/constants';
import helmet from 'helmet';
import compression from 'compression';
import { SecurityService } from './security/security.service';
import {
  ErrorResponseSchema,
  ValidationErrorDetailSchema,
  ValidationErrorResponseSchema,
  AuthenticationErrorResponseSchema,
  NotFoundErrorResponseSchema,
  FileTooLargeErrorResponseSchema,
  InternalServerErrorResponseSchema,
} from './schemas';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    const app = await NestFactory.create(AppModule);

    // Get security service for configuration
    const securityService = app.get(SecurityService);

    // Configure security middleware (helmet) with custom configuration
    app.use(helmet(securityService.getSecurityHeaders()));

    // Enable compression for better performance
    app.use(compression());

    // Configure CORS
    app.enableCors(securityService.getCorsConfiguration());

    // Configure global validation pipe for all endpoints
    app.useGlobalPipes(new ValidationPipe());

    // Configure global exception filter for standardized error responses
    app.useGlobalFilters(new GlobalExceptionFilter());

    // Configure global interceptors for request/response logging
    app.useGlobalInterceptors(new LoggingInterceptor());

    // Configure Swagger/OpenAPI documentation
    const config = new DocumentBuilder()
      .setTitle('Teaching Material System (TMS) REST API')
      .setDescription(
        'A centralized service for storing, retrieving, updating, and deleting F2F paperless marking worked solution quiz materials',
      )
      .setVersion('0.0.1')
      .addBasicAuth(
        {
          type: 'http',
          scheme: 'basic',
          description: 'Basic Authentication using username and password',
        },
        'basic',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-Correlation-ID',
          in: 'header',
          description: 'Correlation ID for request tracing (UUID format)',
        },
        'correlation-id',
      )
      .build();

    const document = SwaggerModule.createDocument(app, config, {
      extraModels: [
        // Include error response schemas for documentation
        ErrorResponseSchema,
        ValidationErrorDetailSchema,
        ValidationErrorResponseSchema,
        AuthenticationErrorResponseSchema,
        NotFoundErrorResponseSchema,
        FileTooLargeErrorResponseSchema,
        InternalServerErrorResponseSchema,
      ],
    });
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
      },
    });

    const port = process.env.PORT || DEFAULTS.PORT;
    await app.listen(port);

    logger.log(`Application is running on: http://localhost:${port}`);
    logger.log(`Health check available at: http://localhost:${port}/health`);
    logger.log(
      `API documentation available at: http://localhost:${port}/api/docs`,
    );
  } catch (error) {
    logger.error(
      'Failed to start application:',
      error instanceof Error ? error.message : String(error),
    );
    process.exit(1);
  }
}

// Start the application with proper error handling
bootstrap().catch((error) => {
  const logger = new Logger('Bootstrap');
  logger.error(
    'Unhandled error during bootstrap:',
    error instanceof Error ? error.message : String(error),
  );
  process.exit(1);
});
