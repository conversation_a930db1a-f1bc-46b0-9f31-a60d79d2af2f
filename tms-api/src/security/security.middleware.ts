/**
 * Security Middleware
 *
 * This middleware provides additional security features including:
 * - Request size validation
 * - Security headers
 * - Request sanitization
 */

import {
  Injectable,
  NestMiddleware,
  PayloadTooLargeException,
  Logger,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { SecurityService } from './security.service';

@Injectable()
export class SecurityMiddleware implements NestMiddleware {
  private readonly logger = new Logger(SecurityMiddleware.name);

  constructor(private securityService: SecurityService) {}

  use(req: Request, res: Response, next: NextFunction) {
    // Validate request size for file uploads
    if (req.headers['content-length']) {
      const contentLength = parseInt(req.headers['content-length'], 10);
      const sizeValidation =
        this.securityService.validateRequestSize(contentLength);

      if (!sizeValidation.isValid) {
        this.logger.warn(
          `Request size validation failed: ${sizeValidation.reason}`,
          {
            correlationId: req.correlationId,
            contentLength,
            url: req.url,
            method: req.method,
          },
        );

        throw new PayloadTooLargeException({
          statusCode: 413,
          message: sizeValidation.reason,
          error: 'Payload Too Large',
        });
      }
    }

    // Add additional security headers (complementing nginx headers)
    res.setHeader('X-API-Version', '1.0.0');
    res.setHeader('X-Request-ID', req.correlationId || 'unknown');

    // Remove potentially sensitive headers
    res.removeHeader('X-Powered-By');
    res.removeHeader('Server');

    next();
  }
}
