/**
 * Security Service
 *
 * This service provides security-related utilities and configurations
 * for the TMS API including input sanitization and security validation.
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SecurityService {
  private readonly logger = new Logger(SecurityService.name);

  constructor(private configService: ConfigService) {}

  /**
   * Sanitizes file upload input to prevent malicious file uploads
   * @param filename - The original filename
   * @param mimetype - The file MIME type
   * @returns Sanitized filename and validation result
   */
  sanitizeFileUpload(
    filename: string,
    mimetype: string,
  ): {
    sanitizedFilename: string;
    isValid: boolean;
    reason: string;
  } {
    // Remove any path traversal attempts
    const sanitizedFilename = filename.replace(/[/\\:*?"<>|]/g, '_');

    // Check for allowed file extensions (ZIP files only for quiz uploads)
    const allowedExtensions = ['.zip'];
    const fileExtension = sanitizedFilename
      .toLowerCase()
      .substring(sanitizedFilename.lastIndexOf('.'));

    if (!allowedExtensions.includes(fileExtension)) {
      return {
        sanitizedFilename,
        isValid: false,
        reason: `File extension ${fileExtension} not allowed. Only ZIP files are permitted.`,
      };
    }

    // Check MIME type
    const allowedMimeTypes = [
      'application/zip',
      'application/x-zip-compressed',
      'application/octet-stream', // Some browsers send this for ZIP files
    ];

    if (!allowedMimeTypes.includes(mimetype)) {
      return {
        sanitizedFilename,
        isValid: false,
        reason: `MIME type ${mimetype} not allowed. Only ZIP files are permitted.`,
      };
    }

    // Check filename length
    if (sanitizedFilename.length > 255) {
      return {
        sanitizedFilename: sanitizedFilename.substring(0, 255),
        isValid: false,
        reason: 'Filename too long. Maximum 255 characters allowed.',
      };
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /\.exe$/i,
      /\.bat$/i,
      /\.cmd$/i,
      /\.scr$/i,
      /\.pif$/i,
      /\.com$/i,
      /\.jar$/i,
      /\.js$/i,
      /\.vbs$/i,
      /\.ps1$/i,
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(sanitizedFilename)) {
        return {
          sanitizedFilename,
          isValid: false,
          reason: 'Filename contains suspicious patterns.',
        };
      }
    }

    return {
      sanitizedFilename,
      isValid: true,
      reason: 'File validation passed',
    };
  }

  /**
   * Gets CORS configuration based on environment
   * @returns CORS configuration object
   */
  getCorsConfiguration() {
    const isProduction =
      this.configService.get<string>('NODE_ENV') === 'production';
    const corsOrigin = this.configService.get<string>('CORS_ORIGIN');

    if (isProduction && corsOrigin) {
      return {
        origin: corsOrigin,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        allowedHeaders: [
          'Content-Type',
          'Authorization',
          'X-Correlation-ID',
          'Accept',
        ],
      };
    }

    // Development configuration - more permissive
    return {
      origin: true,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Correlation-ID',
        'Accept',
      ],
    };
  }

  /**
   * Gets security headers configuration
   * @returns Security headers object
   */
  getSecurityHeaders() {
    const isProduction =
      this.configService.get<string>('NODE_ENV') === 'production';

    return {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'"], // Needed for Swagger UI
          styleSrc: ["'self'", "'unsafe-inline'"], // Needed for Swagger UI
          imgSrc: ["'self'", 'data:', 'https:'],
          fontSrc: ["'self'"],
          connectSrc: ["'self'"],
          frameSrc: ["'none'"],
          objectSrc: ["'none'"],
          baseUri: ["'self'"],
          formAction: ["'self'"],
        },
      },
      crossOriginEmbedderPolicy: false, // Disabled for Swagger UI compatibility
      crossOriginOpenerPolicy: false,
      crossOriginResourcePolicy: { policy: 'cross-origin' as const },
      dnsPrefetchControl: { allow: false },
      frameguard: { action: 'deny' as const },
      hidePoweredBy: true,
      hsts: isProduction
        ? {
            maxAge: 31536000, // 1 year
            includeSubDomains: true,
            preload: true,
          }
        : false,
      ieNoOpen: true,
      noSniff: true,
      originAgentCluster: true,
      permittedCrossDomainPolicies: false,
      referrerPolicy: { policy: 'strict-origin-when-cross-origin' as const },
      xssFilter: true,
    };
  }

  /**
   * Validates request size limits
   * @param contentLength - Content length from request headers
   * @returns Validation result
   */
  validateRequestSize(contentLength: number): {
    isValid: boolean;
    reason: string;
  } {
    const maxSize = 100 * 1024 * 1024; // 100MB limit for ZIP files

    if (contentLength > maxSize) {
      return {
        isValid: false,
        reason: `Request size ${contentLength} bytes exceeds maximum allowed size of ${maxSize} bytes (100MB).`,
      };
    }

    return {
      isValid: true,
      reason: 'Request size validation passed',
    };
  }
}
