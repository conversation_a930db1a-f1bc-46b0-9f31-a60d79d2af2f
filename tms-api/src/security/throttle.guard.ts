/**
 * Custom Throttle Guard
 *
 * This guard extends the default ThrottlerGuard to provide custom
 * rate limiting behavior for different endpoints.
 */

import { Injectable, ExecutionContext } from '@nestjs/common';
import { ThrottlerGuard } from '@nestjs/throttler';
import { Request } from 'express';

@Injectable()
export class CustomThrottlerGuard extends ThrottlerGuard {
  protected getTracker(req: Request): Promise<string> {
    // Use IP address and user agent for tracking to prevent simple bypasses
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';

    // Create a hash of IP + User Agent for more robust tracking
    return Promise.resolve(
      `${ip}-${Buffer.from(userAgent).toString('base64').substring(0, 20)}`,
    );
  }

  protected getThrottlerSuffix(context: ExecutionContext): string {
    const request = context.switchToHttp().getRequest<Request>();

    // Apply different rate limits based on endpoint
    if (request.url.includes('/auth') || request.url.includes('/login')) {
      return 'auth';
    }

    if (request.url.includes('/quiz') && request.method === 'POST') {
      return 'upload';
    }

    return 'default';
  }
}
